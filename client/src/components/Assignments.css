.assignments {
  max-width: 1200px;
  margin: 0 auto;
}

.assignments-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-xl);
}

.assignments-header h1 {
  font-size: 2rem;
  font-weight: 700;
  color: var(--text-primary);
}

.view-controls {
  margin-bottom: var(--spacing-lg);
}

.view-mode-tabs {
  display: flex;
  background-color: var(--bg-primary);
  border-radius: var(--radius-lg);
  padding: var(--spacing-xs);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--border-light);
}

.tab {
  flex: 1;
  padding: var(--spacing-sm) var(--spacing-md);
  border: none;
  background: none;
  color: var(--text-secondary);
  font-weight: 500;
  border-radius: var(--radius-md);
  cursor: pointer;
  transition: all var(--transition-fast);
  text-align: center;
}

.tab:hover {
  color: var(--text-primary);
  background-color: var(--bg-secondary);
}

.tab.active {
  background-color: var(--primary-color);
  color: var(--text-white);
}

.assignments-stats {
  margin-bottom: var(--spacing-lg);
  color: var(--text-secondary);
  font-size: 0.875rem;
}

.assignments-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg);
}

.assignment-card {
  background-color: var(--bg-primary);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--border-light);
  padding: var(--spacing-lg);
  transition: all var(--transition-fast);
}

.assignment-card:hover {
  box-shadow: var(--shadow-md);
}

.assignment-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: var(--spacing-md);
  gap: var(--spacing-md);
}

.assignment-header h3 {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--text-primary);
  line-height: 1.4;
  flex: 1;
}

.assignment-details {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: var(--spacing-lg);
  margin-bottom: var(--spacing-md);
}

.assignment-info p {
  font-size: 0.875rem;
  color: var(--text-secondary);
  margin-bottom: var(--spacing-xs);
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
}

.assignment-info p:last-child {
  margin-bottom: 0;
}

.officer-info {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.officer-avatar {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background: linear-gradient(135deg, var(--primary-color), var(--primary-hover));
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--text-white);
  font-weight: 700;
  font-size: 1rem;
  flex-shrink: 0;
}

.officer-details h4 {
  font-size: 1rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--spacing-xs);
}

.officer-details p {
  font-size: 0.875rem;
  color: var(--text-secondary);
  margin-bottom: var(--spacing-xs);
}

.role-tag {
  display: inline-block;
  padding: var(--spacing-xs) var(--spacing-sm);
  background-color: var(--info-color);
  color: var(--text-white);
  border-radius: var(--radius-sm);
  font-size: 0.75rem;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.assignment-meta {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  gap: var(--spacing-md);
}

.assigned-date {
  font-size: 0.75rem;
  color: var(--text-muted);
}

/* Grouped View Styles */
.grouped-view {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xl);
}

.officer-group,
.case-group {
  background-color: var(--bg-primary);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--border-light);
  overflow: hidden;
}

.group-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-lg);
  background-color: var(--bg-secondary);
  border-bottom: 1px solid var(--border-light);
}

.officer-info,
.case-info {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.case-info h3 {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--spacing-xs);
}

.case-info p {
  font-size: 0.875rem;
  color: var(--text-secondary);
}

.assignment-count {
  font-size: 0.875rem;
  color: var(--text-secondary);
  font-weight: 500;
}

.group-assignments {
  padding: var(--spacing-lg);
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

.mini-assignment-card {
  background-color: var(--bg-secondary);
  border-radius: var(--radius-md);
  border: 1px solid var(--border-light);
  padding: var(--spacing-md);
  transition: all var(--transition-fast);
}

.mini-assignment-card:hover {
  background-color: var(--bg-tertiary);
}

.mini-assignment-card h4 {
  font-size: 1rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--spacing-xs);
}

.mini-assignment-card p {
  font-size: 0.875rem;
  color: var(--text-secondary);
  margin-bottom: var(--spacing-sm);
}

.mini-assignment-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: var(--spacing-sm);
}

.mini-officer-card {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  background-color: var(--bg-secondary);
  border-radius: var(--radius-md);
  border: 1px solid var(--border-light);
  padding: var(--spacing-md);
  transition: all var(--transition-fast);
}

.mini-officer-card:hover {
  background-color: var(--bg-tertiary);
}

.mini-officer-card .officer-avatar {
  width: 40px;
  height: 40px;
  font-size: 0.875rem;
}

.mini-officer-card .officer-info h4 {
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--spacing-xs);
}

.mini-officer-card .officer-info p {
  font-size: 0.75rem;
  color: var(--text-secondary);
  margin-bottom: var(--spacing-xs);
}

/* Responsive Design */
@media (max-width: 768px) {
  .assignments-header {
    flex-direction: column;
    align-items: stretch;
    gap: var(--spacing-md);
  }

  .view-mode-tabs {
    flex-direction: column;
  }

  .assignment-details {
    flex-direction: column;
    align-items: stretch;
    gap: var(--spacing-md);
  }

  .assignment-meta {
    justify-content: flex-start;
  }

  .group-header {
    flex-direction: column;
    align-items: stretch;
    gap: var(--spacing-md);
  }

  .mini-assignment-meta {
    flex-direction: column;
    align-items: flex-start;
  }
}
