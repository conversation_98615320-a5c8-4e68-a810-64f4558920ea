import React, { useState } from "react";
import { Switch, Route } from "react-router-dom";
import Navigation from "./Navigation";
import Dashboard from "./Dashboard";
import CrimeReports from "./CrimeReports";
import Officers from "./Officers";
import Assignments from "./Assignments";
import Login from "./Login";
import { AuthProvider } from "../context/AuthContext";
import "./App.css";

function App() {
  return (
    <AuthProvider>
      <div className="app">
        <Navigation />
        <main className="main-content">
          <Switch>
            <Route exact path="/" component={Dashboard} />
            <Route path="/login" component={Login} />
            <Route path="/reports" component={CrimeReports} />
            <Route path="/officers" component={Officers} />
            <Route path="/assignments" component={Assignments} />
          </Switch>
        </main>
      </div>
    </AuthProvider>
  );
}

export default App;
