.officers {
  max-width: 1200px;
  margin: 0 auto;
}

.officers-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-xl);
}

.officers-header h1 {
  font-size: 2rem;
  font-weight: 700;
  color: var(--text-primary);
}

.officers-stats {
  margin-bottom: var(--spacing-lg);
  color: var(--text-secondary);
  font-size: 0.875rem;
}

.officers-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: var(--spacing-lg);
  margin-bottom: var(--spacing-xl);
}

.officer-card {
  background-color: var(--bg-primary);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--border-light);
  padding: var(--spacing-lg);
  cursor: pointer;
  transition: all var(--transition-fast);
}

.officer-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.officer-header {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-lg);
}

.officer-avatar {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background: linear-gradient(135deg, var(--primary-color), var(--primary-hover));
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--text-white);
  font-weight: 700;
  font-size: 1.25rem;
  flex-shrink: 0;
}

.officer-basic-info h3 {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--spacing-xs);
}

.officer-rank {
  color: var(--text-secondary);
  font-size: 0.875rem;
  margin-bottom: var(--spacing-sm);
}

.officer-details p {
  font-size: 0.875rem;
  color: var(--text-secondary);
  margin-bottom: var(--spacing-xs);
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
}

.officer-details p:last-child {
  margin-bottom: 0;
}

.officer-joined {
  margin-top: var(--spacing-md);
  padding-top: var(--spacing-md);
  border-top: 1px solid var(--border-light);
}

.officer-joined p {
  font-size: 0.75rem;
  color: var(--text-muted);
}

/* Profile Modal Styles */
.profile-avatar {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background: linear-gradient(135deg, var(--primary-color), var(--primary-hover));
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--text-white);
  font-weight: 700;
  font-size: 1.5rem;
  margin: 0 auto var(--spacing-md);
}

.officer-profile {
  text-align: center;
  margin-bottom: var(--spacing-xl);
}

.profile-info h3 {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--spacing-xs);
}

.profile-rank {
  color: var(--text-secondary);
  font-size: 1rem;
  margin-bottom: var(--spacing-sm);
}

.officer-info-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-xl);
}

.current-assignments {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

.assignment-item {
  background-color: var(--bg-secondary);
  border-radius: var(--radius-md);
  border: 1px solid var(--border-light);
  padding: var(--spacing-md);
}

.assignment-info h4 {
  font-size: 1rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--spacing-xs);
}

.assignment-info p {
  font-size: 0.875rem;
  color: var(--text-secondary);
  margin-bottom: var(--spacing-sm);
}

.assignment-role {
  display: inline-block;
  padding: var(--spacing-xs) var(--spacing-sm);
  background-color: var(--primary-color);
  color: var(--text-white);
  border-radius: var(--radius-sm);
  font-size: 0.75rem;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.assignment-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: var(--spacing-sm);
  gap: var(--spacing-sm);
}

.assignment-date {
  font-size: 0.75rem;
  color: var(--text-muted);
}

.no-assignments {
  text-align: center;
  color: var(--text-secondary);
  font-style: italic;
  padding: var(--spacing-lg);
}

/* Responsive Design */
@media (max-width: 768px) {
  .officers-grid {
    grid-template-columns: 1fr;
  }

  .officers-header {
    flex-direction: column;
    align-items: stretch;
    gap: var(--spacing-md);
  }

  .officer-info-grid {
    grid-template-columns: 1fr;
  }

  .assignment-meta {
    flex-direction: column;
    align-items: flex-start;
  }
}
