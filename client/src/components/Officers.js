import React, { useState, useContext } from "react";
import { AuthContext } from "../context/AuthContext";
import { 
  policeOfficers, 
  getOfficerAssignments,
  getOfficersByRole 
} from "../data/mockData";
import "./Officers.css";

function Officers() {
  const { user } = useContext(AuthContext);
  const [selectedRole, setSelectedRole] = useState("all");
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedOfficer, setSelectedOfficer] = useState(null);

  if (!user) {
    return <div className="unauthorized">Please log in to view officers.</div>;
  }

  // Filter officers based on selected filters
  let filteredOfficers = policeOfficers;

  if (selectedRole !== "all") {
    filteredOfficers = filteredOfficers.filter(officer => officer.role === selectedRole);
  }

  if (searchTerm) {
    filteredOfficers = filteredOfficers.filter(officer =>
      officer.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      officer.badge_number.includes(searchTerm) ||
      officer.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
      officer.rank.toLowerCase().includes(searchTerm.toLowerCase())
    );
  }

  const handleOfficerClick = (officer) => {
    setSelectedOfficer(officer);
  };

  const closeModal = () => {
    setSelectedOfficer(null);
  };

  return (
    <div className="officers">
      <div className="officers-header">
        <h1>Police Officers</h1>
        {user.role === 'admin' && (
          <button className="btn btn-primary">+ Add Officer</button>
        )}
      </div>

      <div className="filters-section">
        <div className="search-bar">
          <input
            type="text"
            placeholder="Search officers..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="search-input"
          />
        </div>
        
        <div className="filters">
          <select 
            value={selectedRole} 
            onChange={(e) => setSelectedRole(e.target.value)}
            className="filter-select"
          >
            <option value="all">All Roles</option>
            <option value="admin">Admin</option>
            <option value="officer">Officer</option>
          </select>
        </div>
      </div>

      <div className="officers-stats">
        <span>Showing {filteredOfficers.length} of {policeOfficers.length} officers</span>
      </div>

      <div className="officers-grid">
        {filteredOfficers.map(officer => {
          const officerAssignments = getOfficerAssignments(officer.id);
          return (
            <div 
              key={officer.id} 
              className="officer-card"
              onClick={() => handleOfficerClick(officer)}
            >
              <div className="officer-header">
                <div className="officer-avatar">
                  {officer.name.split(' ').map(n => n[0]).join('')}
                </div>
                <div className="officer-basic-info">
                  <h3>{officer.name}</h3>
                  <p className="officer-rank">{officer.rank}</p>
                  <span className={`role-badge role-${officer.role}`}>
                    {officer.role.toUpperCase()}
                  </span>
                </div>
              </div>
              
              <div className="officer-details">
                <p className="officer-badge">🆔 Badge: {officer.badge_number}</p>
                <p className="officer-email">📧 {officer.email}</p>
                <p className="officer-phone">📞 {officer.phone}</p>
                <p className="officer-assignments">
                  📋 {officerAssignments.length} active assignment(s)
                </p>
              </div>
              
              <div className="officer-joined">
                <p>Joined: {new Date(officer.created_at).toLocaleDateString()}</p>
              </div>
            </div>
          );
        })}
      </div>

      {filteredOfficers.length === 0 && (
        <div className="no-results">
          <h3>No officers found</h3>
          <p>Try adjusting your search criteria or filters.</p>
        </div>
      )}

      {/* Officer Detail Modal */}
      {selectedOfficer && (
        <div className="modal-overlay" onClick={closeModal}>
          <div className="modal-content" onClick={(e) => e.stopPropagation()}>
            <div className="modal-header">
              <h2>{selectedOfficer.name}</h2>
              <button className="close-btn" onClick={closeModal}>×</button>
            </div>
            
            <div className="modal-body">
              <div className="officer-profile">
                <div className="profile-avatar">
                  {selectedOfficer.name.split(' ').map(n => n[0]).join('')}
                </div>
                <div className="profile-info">
                  <h3>{selectedOfficer.name}</h3>
                  <p className="profile-rank">{selectedOfficer.rank}</p>
                  <span className={`role-badge role-${selectedOfficer.role}`}>
                    {selectedOfficer.role.toUpperCase()}
                  </span>
                </div>
              </div>
              
              <div className="officer-info-grid">
                <div className="info-item">
                  <label>Badge Number:</label>
                  <span>{selectedOfficer.badge_number}</span>
                </div>
                <div className="info-item">
                  <label>Email:</label>
                  <span>{selectedOfficer.email}</span>
                </div>
                <div className="info-item">
                  <label>Phone:</label>
                  <span>{selectedOfficer.phone}</span>
                </div>
                <div className="info-item">
                  <label>Joined:</label>
                  <span>{new Date(selectedOfficer.created_at).toLocaleDateString()}</span>
                </div>
              </div>
              
              <div className="assignments-section">
                <label>Current Assignments:</label>
                <div className="current-assignments">
                  {getOfficerAssignments(selectedOfficer.id).map(assignment => (
                    <div key={assignment.id} className="assignment-item">
                      <div className="assignment-info">
                        <h4>{assignment.crime_report.title}</h4>
                        <p>{assignment.crime_report.location}</p>
                        <span className="assignment-role">{assignment.role_in_case}</span>
                      </div>
                      <div className="assignment-meta">
                        <span className={`status-badge status-${assignment.crime_report.status}`}>
                          {assignment.crime_report.status.toUpperCase()}
                        </span>
                        <span className="assignment-date">
                          Assigned: {new Date(assignment.assigned_at).toLocaleDateString()}
                        </span>
                      </div>
                    </div>
                  ))}
                  {getOfficerAssignments(selectedOfficer.id).length === 0 && (
                    <p className="no-assignments">No current assignments</p>
                  )}
                </div>
              </div>
            </div>
            
            <div className="modal-footer">
              <button className="btn btn-secondary" onClick={closeModal}>
                Close
              </button>
              {user.role === 'admin' && (
                <>
                  <button className="btn btn-primary">Edit Officer</button>
                  <button className="btn btn-warning">Assign Case</button>
                </>
              )}
            </div>
          </div>
        </div>
      )}
    </div>
  );
}

export default Officers;
