.navbar {
  background-color: var(--bg-primary);
  border-bottom: 1px solid var(--border-light);
  box-shadow: var(--shadow-sm);
  position: sticky;
  top: 0;
  z-index: 100;
}

.nav-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--spacing-lg);
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 64px;
}

.nav-logo {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--primary-color);
  text-decoration: none;
  transition: color var(--transition-fast);
}

.nav-logo:hover {
  color: var(--primary-hover);
}

.nav-menu {
  display: flex;
  align-items: center;
  gap: var(--spacing-lg);
}

.nav-link {
  color: var(--text-secondary);
  text-decoration: none;
  font-weight: 500;
  padding: var(--spacing-sm) var(--spacing-md);
  border-radius: var(--radius-md);
  transition: all var(--transition-fast);
}

.nav-link:hover {
  color: var(--primary-color);
  background-color: var(--bg-secondary);
}

.nav-link.active {
  color: var(--primary-color);
  background-color: var(--bg-secondary);
}

.nav-user {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  padding-left: var(--spacing-lg);
  border-left: 1px solid var(--border-light);
}

.user-info {
  color: var(--text-secondary);
  font-size: 0.875rem;
}

.logout-btn {
  background: none;
  border: 1px solid var(--border-medium);
  color: var(--text-secondary);
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-md);
  font-size: 0.875rem;
  cursor: pointer;
  transition: all var(--transition-fast);
}

.logout-btn:hover {
  background-color: var(--danger-color);
  color: var(--text-white);
  border-color: var(--danger-color);
}

/* Mobile Navigation */
@media (max-width: 768px) {
  .nav-container {
    padding: 0 var(--spacing-md);
    flex-wrap: wrap;
    height: auto;
    min-height: 64px;
  }

  .nav-menu {
    flex-direction: column;
    width: 100%;
    gap: var(--spacing-sm);
    padding: var(--spacing-md) 0;
    border-top: 1px solid var(--border-light);
    margin-top: var(--spacing-md);
  }

  .nav-user {
    border-left: none;
    border-top: 1px solid var(--border-light);
    padding-left: 0;
    padding-top: var(--spacing-md);
    width: 100%;
    justify-content: space-between;
  }

  .nav-logo {
    font-size: 1.25rem;
  }
}
