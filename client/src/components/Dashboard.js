import React, { useContext } from "react";
import { <PERSON> } from "react-router-dom";
import { AuthContext } from "../context/AuthContext";
import { 
  getStatistics, 
  crimeReports, 
  assignments, 
  getReportsByStatus 
} from "../data/mockData";
import "./Dashboard.css";

function Dashboard() {
  const { user } = useContext(AuthContext);
  const stats = getStatistics();
  const recentReports = crimeReports.slice(0, 5);
  const openReports = getReportsByStatus('open');
  const pendingReports = getReportsByStatus('pending');

  if (!user) {
    return (
      <div className="dashboard">
        <div className="welcome-section">
          <h1>Welcome to Community Watch</h1>
          <p>Please log in to access the police management system.</p>
          <Link to="/login" className="btn btn-primary">
            Login
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="dashboard">
      <div className="dashboard-header">
        <h1>Dashboard</h1>
        <p>Welcome back, {user.name}</p>
      </div>

      <div className="stats-grid">
        <div className="stat-card">
          <h3>Total Reports</h3>
          <div className="stat-number">{stats.totalReports}</div>
        </div>
        <div className="stat-card open">
          <h3>Open Cases</h3>
          <div className="stat-number">{stats.openReports}</div>
        </div>
        <div className="stat-card pending">
          <h3>Pending Cases</h3>
          <div className="stat-number">{stats.pendingReports}</div>
        </div>
        <div className="stat-card closed">
          <h3>Closed Cases</h3>
          <div className="stat-number">{stats.closedReports}</div>
        </div>
        <div className="stat-card">
          <h3>Total Officers</h3>
          <div className="stat-number">{stats.totalOfficers}</div>
        </div>
        <div className="stat-card">
          <h3>Active Assignments</h3>
          <div className="stat-number">{stats.totalAssignments}</div>
        </div>
      </div>

      <div className="dashboard-content">
        <div className="dashboard-section">
          <div className="section-header">
            <h2>Recent Crime Reports</h2>
            <Link to="/reports" className="view-all-link">View All</Link>
          </div>
          <div className="reports-list">
            {recentReports.map(report => (
              <div key={report.id} className={`report-item status-${report.status}`}>
                <div className="report-info">
                  <h4>{report.title}</h4>
                  <p className="report-location">{report.location}</p>
                  <p className="report-category">{report.crime_category.name}</p>
                </div>
                <div className="report-meta">
                  <span className={`status-badge status-${report.status}`}>
                    {report.status.toUpperCase()}
                  </span>
                  <span className="report-date">
                    {new Date(report.created_at).toLocaleDateString()}
                  </span>
                </div>
              </div>
            ))}
          </div>
        </div>

        <div className="dashboard-section">
          <div className="section-header">
            <h2>Priority Cases</h2>
            <Link to="/reports?status=open" className="view-all-link">View All Open</Link>
          </div>
          <div className="priority-cases">
            {openReports.slice(0, 3).map(report => (
              <div key={report.id} className="priority-case">
                <h4>{report.title}</h4>
                <p>{report.location}</p>
                <div className="case-meta">
                  <span className="category">{report.crime_category.name}</span>
                  <span className="date">
                    {new Date(report.created_at).toLocaleDateString()}
                  </span>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      <div className="quick-actions">
        <h2>Quick Actions</h2>
        <div className="action-buttons">
          <Link to="/reports/new" className="btn btn-primary">
            New Crime Report
          </Link>
          <Link to="/assignments" className="btn btn-secondary">
            Manage Assignments
          </Link>
          {user.role === 'admin' && (
            <Link to="/officers" className="btn btn-secondary">
              Manage Officers
            </Link>
          )}
        </div>
      </div>
    </div>
  );
}

export default Dashboard;
