.dashboard {
  max-width: 1200px;
  margin: 0 auto;
}

.dashboard-header {
  margin-bottom: var(--spacing-2xl);
}

.dashboard-header h1 {
  font-size: 2rem;
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: var(--spacing-xs);
}

.dashboard-header p {
  color: var(--text-secondary);
  font-size: 1rem;
}

.welcome-section {
  text-align: center;
  padding: var(--spacing-2xl);
  background-color: var(--bg-primary);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
}

.welcome-section h1 {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: var(--spacing-md);
}

.welcome-section p {
  color: var(--text-secondary);
  font-size: 1.125rem;
  margin-bottom: var(--spacing-xl);
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--spacing-lg);
  margin-bottom: var(--spacing-2xl);
}

.stat-card {
  background-color: var(--bg-primary);
  padding: var(--spacing-lg);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--border-light);
  text-align: center;
  transition: transform var(--transition-fast);
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.stat-card h3 {
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--text-secondary);
  margin-bottom: var(--spacing-sm);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.stat-number {
  font-size: 2rem;
  font-weight: 700;
  color: var(--text-primary);
}

.stat-card.open .stat-number {
  color: var(--status-open);
}

.stat-card.pending .stat-number {
  color: var(--status-pending);
}

.stat-card.closed .stat-number {
  color: var(--status-closed);
}

.dashboard-content {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: var(--spacing-xl);
  margin-bottom: var(--spacing-2xl);
}

.dashboard-section {
  background-color: var(--bg-primary);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--border-light);
  overflow: hidden;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-lg);
  border-bottom: 1px solid var(--border-light);
}

.section-header h2 {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-primary);
}

.view-all-link {
  color: var(--primary-color);
  text-decoration: none;
  font-size: 0.875rem;
  font-weight: 500;
  transition: color var(--transition-fast);
}

.view-all-link:hover {
  color: var(--primary-hover);
}

.reports-list {
  padding: var(--spacing-lg);
}

.report-item {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: var(--spacing-md);
  border-radius: var(--radius-md);
  margin-bottom: var(--spacing-md);
  border-left: 4px solid var(--border-light);
  background-color: var(--bg-secondary);
  transition: all var(--transition-fast);
}

.report-item:last-child {
  margin-bottom: 0;
}

.report-item:hover {
  background-color: var(--bg-tertiary);
  transform: translateX(4px);
}

.report-item.status-open {
  border-left-color: var(--status-open);
}

.report-item.status-pending {
  border-left-color: var(--status-pending);
}

.report-item.status-closed {
  border-left-color: var(--status-closed);
}

.report-info h4 {
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--spacing-xs);
}

.report-location,
.report-category {
  font-size: 0.75rem;
  color: var(--text-secondary);
  margin-bottom: var(--spacing-xs);
}

.report-meta {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: var(--spacing-xs);
}

.report-date {
  font-size: 0.75rem;
  color: var(--text-muted);
}

.priority-cases {
  padding: var(--spacing-lg);
}

.priority-case {
  padding: var(--spacing-md);
  border-radius: var(--radius-md);
  background-color: var(--bg-secondary);
  margin-bottom: var(--spacing-md);
  border: 1px solid var(--border-light);
  transition: all var(--transition-fast);
}

.priority-case:last-child {
  margin-bottom: 0;
}

.priority-case:hover {
  background-color: var(--bg-tertiary);
  border-color: var(--primary-color);
}

.priority-case h4 {
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--spacing-xs);
}

.priority-case p {
  font-size: 0.75rem;
  color: var(--text-secondary);
  margin-bottom: var(--spacing-sm);
}

.case-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.case-meta .category {
  font-size: 0.75rem;
  color: var(--primary-color);
  font-weight: 500;
}

.case-meta .date {
  font-size: 0.75rem;
  color: var(--text-muted);
}

.quick-actions {
  background-color: var(--bg-primary);
  padding: var(--spacing-xl);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--border-light);
}

.quick-actions h2 {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--spacing-lg);
}

.action-buttons {
  display: flex;
  gap: var(--spacing-md);
  flex-wrap: wrap;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .dashboard-content {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .action-buttons {
    flex-direction: column;
  }

  .report-item {
    flex-direction: column;
    gap: var(--spacing-sm);
  }

  .report-meta {
    align-items: flex-start;
    flex-direction: row;
    justify-content: space-between;
    width: 100%;
  }
}

@media (max-width: 480px) {
  .stats-grid {
    grid-template-columns: 1fr;
  }

  .dashboard-header h1 {
    font-size: 1.5rem;
  }

  .welcome-section h1 {
    font-size: 2rem;
  }
}
