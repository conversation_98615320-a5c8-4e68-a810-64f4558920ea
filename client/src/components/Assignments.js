import React, { useState, useContext } from "react";
import { AuthContext } from "../context/AuthContext";
import { 
  assignments, 
  policeOfficers, 
  crimeReports,
  getOfficerAssignments,
  getReportAssignments 
} from "../data/mockData";
import "./Assignments.css";

function Assignments() {
  const { user } = useContext(AuthContext);
  const [viewMode, setViewMode] = useState("all"); // all, by-officer, by-case
  const [selectedOfficer, setSelectedOfficer] = useState("all");
  const [selectedStatus, setSelectedStatus] = useState("all");
  const [searchTerm, setSearchTerm] = useState("");

  if (!user) {
    return <div className="unauthorized">Please log in to view assignments.</div>;
  }

  // Filter assignments based on selected filters
  let filteredAssignments = assignments;

  if (selectedOfficer !== "all") {
    filteredAssignments = filteredAssignments.filter(assignment => 
      assignment.officer_id === parseInt(selectedOfficer)
    );
  }

  if (selectedStatus !== "all") {
    filteredAssignments = filteredAssignments.filter(assignment => 
      assignment.crime_report.status === selectedStatus
    );
  }

  if (searchTerm) {
    filteredAssignments = filteredAssignments.filter(assignment =>
      assignment.crime_report.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      assignment.officer.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      assignment.role_in_case.toLowerCase().includes(searchTerm.toLowerCase())
    );
  }

  const groupedByOfficer = policeOfficers.map(officer => ({
    officer,
    assignments: getOfficerAssignments(officer.id)
  }));

  const groupedByCase = crimeReports.map(report => ({
    report,
    assignments: getReportAssignments(report.id)
  }));

  return (
    <div className="assignments">
      <div className="assignments-header">
        <h1>Case Assignments</h1>
        {user.role === 'admin' && (
          <button className="btn btn-primary">+ New Assignment</button>
        )}
      </div>

      <div className="view-controls">
        <div className="view-mode-tabs">
          <button 
            className={`tab ${viewMode === 'all' ? 'active' : ''}`}
            onClick={() => setViewMode('all')}
          >
            All Assignments
          </button>
          <button 
            className={`tab ${viewMode === 'by-officer' ? 'active' : ''}`}
            onClick={() => setViewMode('by-officer')}
          >
            By Officer
          </button>
          <button 
            className={`tab ${viewMode === 'by-case' ? 'active' : ''}`}
            onClick={() => setViewMode('by-case')}
          >
            By Case
          </button>
        </div>
      </div>

      {viewMode === 'all' && (
        <>
          <div className="filters-section">
            <div className="search-bar">
              <input
                type="text"
                placeholder="Search assignments..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="search-input"
              />
            </div>
            
            <div className="filters">
              <select 
                value={selectedOfficer} 
                onChange={(e) => setSelectedOfficer(e.target.value)}
                className="filter-select"
              >
                <option value="all">All Officers</option>
                {policeOfficers.map(officer => (
                  <option key={officer.id} value={officer.id}>
                    {officer.name}
                  </option>
                ))}
              </select>

              <select 
                value={selectedStatus} 
                onChange={(e) => setSelectedStatus(e.target.value)}
                className="filter-select"
              >
                <option value="all">All Status</option>
                <option value="open">Open</option>
                <option value="pending">Pending</option>
                <option value="closed">Closed</option>
              </select>
            </div>
          </div>

          <div className="assignments-stats">
            <span>Showing {filteredAssignments.length} of {assignments.length} assignments</span>
          </div>

          <div className="assignments-list">
            {filteredAssignments.map(assignment => (
              <div key={assignment.id} className="assignment-card">
                <div className="assignment-header">
                  <h3>{assignment.crime_report.title}</h3>
                  <span className={`status-badge status-${assignment.crime_report.status}`}>
                    {assignment.crime_report.status.toUpperCase()}
                  </span>
                </div>
                
                <div className="assignment-details">
                  <div className="assignment-info">
                    <p className="case-location">📍 {assignment.crime_report.location}</p>
                    <p className="case-category">🏷️ {assignment.crime_report.crime_category.name}</p>
                    <p className="case-date">
                      📅 Reported: {new Date(assignment.crime_report.created_at).toLocaleDateString()}
                    </p>
                  </div>
                  
                  <div className="officer-info">
                    <div className="officer-avatar">
                      {assignment.officer.name.split(' ').map(n => n[0]).join('')}
                    </div>
                    <div className="officer-details">
                      <h4>{assignment.officer.name}</h4>
                      <p>{assignment.officer.rank}</p>
                      <span className="role-tag">{assignment.role_in_case}</span>
                    </div>
                  </div>
                </div>
                
                <div className="assignment-meta">
                  <span className="assigned-date">
                    Assigned: {new Date(assignment.assigned_at).toLocaleDateString()}
                  </span>
                </div>
              </div>
            ))}
          </div>
        </>
      )}

      {viewMode === 'by-officer' && (
        <div className="grouped-view">
          {groupedByOfficer.map(({ officer, assignments }) => (
            <div key={officer.id} className="officer-group">
              <div className="group-header">
                <div className="officer-info">
                  <div className="officer-avatar">
                    {officer.name.split(' ').map(n => n[0]).join('')}
                  </div>
                  <div>
                    <h3>{officer.name}</h3>
                    <p>{officer.rank} - {officer.role}</p>
                  </div>
                </div>
                <span className="assignment-count">
                  {assignments.length} assignment(s)
                </span>
              </div>
              
              <div className="group-assignments">
                {assignments.map(assignment => (
                  <div key={assignment.id} className="mini-assignment-card">
                    <h4>{assignment.crime_report.title}</h4>
                    <p>{assignment.crime_report.location}</p>
                    <div className="mini-assignment-meta">
                      <span className="role-tag">{assignment.role_in_case}</span>
                      <span className={`status-badge status-${assignment.crime_report.status}`}>
                        {assignment.crime_report.status.toUpperCase()}
                      </span>
                    </div>
                  </div>
                ))}
                {assignments.length === 0 && (
                  <p className="no-assignments">No current assignments</p>
                )}
              </div>
            </div>
          ))}
        </div>
      )}

      {viewMode === 'by-case' && (
        <div className="grouped-view">
          {groupedByCase.filter(({ assignments }) => assignments.length > 0).map(({ report, assignments }) => (
            <div key={report.id} className="case-group">
              <div className="group-header">
                <div className="case-info">
                  <h3>{report.title}</h3>
                  <p>{report.location}</p>
                  <span className={`status-badge status-${report.status}`}>
                    {report.status.toUpperCase()}
                  </span>
                </div>
                <span className="assignment-count">
                  {assignments.length} officer(s) assigned
                </span>
              </div>
              
              <div className="group-assignments">
                {assignments.map(assignment => (
                  <div key={assignment.id} className="mini-officer-card">
                    <div className="officer-avatar">
                      {assignment.officer.name.split(' ').map(n => n[0]).join('')}
                    </div>
                    <div className="officer-info">
                      <h4>{assignment.officer.name}</h4>
                      <p>{assignment.officer.rank}</p>
                      <span className="role-tag">{assignment.role_in_case}</span>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          ))}
        </div>
      )}

      {((viewMode === 'all' && filteredAssignments.length === 0) ||
        (viewMode === 'by-officer' && groupedByOfficer.every(g => g.assignments.length === 0)) ||
        (viewMode === 'by-case' && groupedByCase.every(g => g.assignments.length === 0))) && (
        <div className="no-results">
          <h3>No assignments found</h3>
          <p>Try adjusting your search criteria or filters.</p>
        </div>
      )}
    </div>
  );
}

export default Assignments;
