import React, { useState, useContext } from "react";
import { AuthContext } from "../context/AuthContext";
import { 
  crimeReports, 
  crimeCategories, 
  getReportsByStatus, 
  getReportsByCategory,
  getReportAssignments 
} from "../data/mockData";
import "./CrimeReports.css";

function CrimeReports() {
  const { user } = useContext(AuthContext);
  const [selectedStatus, setSelectedStatus] = useState("all");
  const [selectedCategory, setSelectedCategory] = useState("all");
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedReport, setSelectedReport] = useState(null);

  if (!user) {
    return <div className="unauthorized">Please log in to view crime reports.</div>;
  }

  // Filter reports based on selected filters
  let filteredReports = crimeReports;

  if (selectedStatus !== "all") {
    filteredReports = filteredReports.filter(report => report.status === selectedStatus);
  }

  if (selectedCategory !== "all") {
    filteredReports = filteredReports.filter(report => 
      report.crime_category_id === parseInt(selectedCategory)
    );
  }

  if (searchTerm) {
    filteredReports = filteredReports.filter(report =>
      report.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      report.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
      report.location.toLowerCase().includes(searchTerm.toLowerCase())
    );
  }

  const handleReportClick = (report) => {
    setSelectedReport(report);
  };

  const closeModal = () => {
    setSelectedReport(null);
  };

  return (
    <div className="crime-reports">
      <div className="reports-header">
        <h1>Crime Reports</h1>
        <button className="btn btn-primary">+ New Report</button>
      </div>

      <div className="filters-section">
        <div className="search-bar">
          <input
            type="text"
            placeholder="Search reports..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="search-input"
          />
        </div>
        
        <div className="filters">
          <select 
            value={selectedStatus} 
            onChange={(e) => setSelectedStatus(e.target.value)}
            className="filter-select"
          >
            <option value="all">All Status</option>
            <option value="open">Open</option>
            <option value="pending">Pending</option>
            <option value="closed">Closed</option>
          </select>

          <select 
            value={selectedCategory} 
            onChange={(e) => setSelectedCategory(e.target.value)}
            className="filter-select"
          >
            <option value="all">All Categories</option>
            {crimeCategories.map(category => (
              <option key={category.id} value={category.id}>
                {category.name}
              </option>
            ))}
          </select>
        </div>
      </div>

      <div className="reports-stats">
        <span>Showing {filteredReports.length} of {crimeReports.length} reports</span>
      </div>

      <div className="reports-grid">
        {filteredReports.map(report => {
          const reportAssignments = getReportAssignments(report.id);
          return (
            <div 
              key={report.id} 
              className={`report-card status-${report.status}`}
              onClick={() => handleReportClick(report)}
            >
              <div className="report-header">
                <h3>{report.title}</h3>
                <span className={`status-badge status-${report.status}`}>
                  {report.status.toUpperCase()}
                </span>
              </div>
              
              <div className="report-details">
                <p className="report-location">📍 {report.location}</p>
                <p className="report-category">🏷️ {report.crime_category.name}</p>
                <p className="report-date">
                  📅 {new Date(report.created_at).toLocaleDateString()}
                </p>
                <p className="report-assignments">
                  👮 {reportAssignments.length} officer(s) assigned
                </p>
              </div>
              
              <div className="report-description">
                <p>{report.description.substring(0, 100)}...</p>
              </div>
            </div>
          );
        })}
      </div>

      {filteredReports.length === 0 && (
        <div className="no-results">
          <h3>No reports found</h3>
          <p>Try adjusting your search criteria or filters.</p>
        </div>
      )}

      {/* Report Detail Modal */}
      {selectedReport && (
        <div className="modal-overlay" onClick={closeModal}>
          <div className="modal-content" onClick={(e) => e.stopPropagation()}>
            <div className="modal-header">
              <h2>{selectedReport.title}</h2>
              <button className="close-btn" onClick={closeModal}>×</button>
            </div>
            
            <div className="modal-body">
              <div className="report-info-grid">
                <div className="info-item">
                  <label>Status:</label>
                  <span className={`status-badge status-${selectedReport.status}`}>
                    {selectedReport.status.toUpperCase()}
                  </span>
                </div>
                <div className="info-item">
                  <label>Category:</label>
                  <span>{selectedReport.crime_category.name}</span>
                </div>
                <div className="info-item">
                  <label>Location:</label>
                  <span>{selectedReport.location}</span>
                </div>
                <div className="info-item">
                  <label>Date:</label>
                  <span>{new Date(selectedReport.created_at).toLocaleString()}</span>
                </div>
              </div>
              
              <div className="description-section">
                <label>Description:</label>
                <p>{selectedReport.description}</p>
              </div>
              
              <div className="assignments-section">
                <label>Assigned Officers:</label>
                <div className="assigned-officers">
                  {getReportAssignments(selectedReport.id).map(assignment => (
                    <div key={assignment.id} className="officer-assignment">
                      <span className="officer-name">{assignment.officer.name}</span>
                      <span className="officer-role">({assignment.role_in_case})</span>
                    </div>
                  ))}
                </div>
              </div>
            </div>
            
            <div className="modal-footer">
              <button className="btn btn-secondary" onClick={closeModal}>
                Close
              </button>
              {user.role === 'admin' && (
                <>
                  <button className="btn btn-primary">Edit Report</button>
                  <button className="btn btn-warning">Assign Officer</button>
                </>
              )}
            </div>
          </div>
        </div>
      )}
    </div>
  );
}

export default CrimeReports;
