.login-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-hover) 100%);
  padding: var(--spacing-lg);
}

.login-card {
  background-color: var(--bg-primary);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-lg);
  padding: var(--spacing-2xl);
  width: 100%;
  max-width: 400px;
}

.login-card h2 {
  text-align: center;
  margin-bottom: var(--spacing-sm);
  color: var(--text-primary);
  font-size: 1.75rem;
  font-weight: 700;
}

.login-subtitle {
  text-align: center;
  color: var(--text-secondary);
  margin-bottom: var(--spacing-2xl);
  font-size: 0.875rem;
}

.login-form {
  margin-bottom: var(--spacing-xl);
}

.login-btn {
  width: 100%;
  padding: var(--spacing-md);
  font-size: 1rem;
  font-weight: 600;
  margin-top: var(--spacing-md);
}

.error-message {
  background-color: #fef2f2;
  color: var(--danger-color);
  padding: var(--spacing-sm) var(--spacing-md);
  border-radius: var(--radius-md);
  border: 1px solid #fecaca;
  margin-bottom: var(--spacing-md);
  font-size: 0.875rem;
  text-align: center;
}

.demo-credentials {
  background-color: var(--bg-secondary);
  padding: var(--spacing-lg);
  border-radius: var(--radius-md);
  border: 1px solid var(--border-light);
}

.demo-credentials h4 {
  margin-bottom: var(--spacing-sm);
  color: var(--text-primary);
  font-size: 0.875rem;
  font-weight: 600;
}

.demo-credentials p {
  margin-bottom: var(--spacing-xs);
  font-size: 0.75rem;
  color: var(--text-secondary);
}

.demo-credentials p:last-child {
  margin-bottom: 0;
}

.demo-credentials strong {
  color: var(--text-primary);
}

@media (max-width: 480px) {
  .login-container {
    padding: var(--spacing-md);
  }

  .login-card {
    padding: var(--spacing-xl);
  }

  .login-card h2 {
    font-size: 1.5rem;
  }
}
