import React, { useContext } from "react";
import { Link, useHistory } from "react-router-dom";
import { AuthContext } from "../context/AuthContext";
import "./Navigation.css";

function Navigation() {
  const { user, logout } = useContext(AuthContext);
  const history = useHistory();

  const handleLogout = () => {
    logout();
    history.push("/login");
  };

  if (!user) {
    return (
      <nav className="navbar">
        <div className="nav-container">
          <Link to="/" className="nav-logo">
            Community Watch
          </Link>
          <div className="nav-menu">
            <Link to="/login" className="nav-link">
              Login
            </Link>
          </div>
        </div>
      </nav>
    );
  }

  return (
    <nav className="navbar">
      <div className="nav-container">
        <Link to="/" className="nav-logo">
          Community Watch
        </Link>
        <div className="nav-menu">
          <Link to="/" className="nav-link">
            Dashboard
          </Link>
          <Link to="/reports" className="nav-link">
            Crime Reports
          </Link>
          <Link to="/officers" className="nav-link">
            Officers
          </Link>
          <Link to="/assignments" className="nav-link">
            Assignments
          </Link>
          <div className="nav-user">
            <span className="user-info">
              {user.name} ({user.role})
            </span>
            <button onClick={handleLogout} className="logout-btn">
              Logout
            </button>
          </div>
        </div>
      </div>
    </nav>
  );
}

export default Navigation;
