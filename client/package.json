{"name": "client", "version": "0.1.0", "private": true, "proxy": "http://localhost:5555", "dependencies": {"@testing-library/jest-dom": "^5.17.0", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^5.2.0", "react-scripts": "5.0.1", "web-vitals": "^2.1.4"}, "devDependencies": {"@babel/plugin-proposal-private-property-in-object": "^7.21.11"}, "scripts": {"start": "PORT=4000 react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}